2025-07-08 12:02:37,435 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 12:02:37,435 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 12:02:38,166 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 12:02:38,166 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:02:38,356 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:02:38,356 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 12:02:39,586 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:02:39,586 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 12:02:39,586 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:02:39,716 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:02:39,716 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:02:39,716 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 12:02:39,723 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 12:02:39,725 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 12:02:39,735 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 12:02:39,738 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:39,897 - WARNING - tod_display - tod_display.py:315 - No ToD generation vs consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:39,897 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:39,897 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 12:02:40,064 - WARNING - tod_display - tod_display.py:370 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:40,064 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:40,064 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 12:02:40,231 - WARNING - tod_display - tod_display.py:435 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:40,231 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 12:02:40,382 - WARNING - tod_display - tod_display.py:496 - No hourly generation data available for plant: Kids Clinic India Limited
2025-07-08 12:02:49,069 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 12:02:49,069 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 12:02:49,819 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 12:02:49,819 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:02:49,938 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:02:49,941 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 12:02:51,122 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:02:51,122 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 12:02:51,123 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:02:51,139 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:02:51,139 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:02:51,147 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 12:02:51,147 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 12:02:51,149 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 12:02:51,156 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 12:02:51,156 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:02:51,656 - INFO - tod_display - tod_display.py:319 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 12:02:51,656 - INFO - tod_display - tod_display.py:328 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:02:52,031 - INFO - tod_display - tod_display.py:332 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:02:52,048 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:02:52,048 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 12:02:52,561 - INFO - tod_display - tod_display.py:374 - Successfully fetched 120 records for ToD generation - Kids Clinic India Limited
2025-07-08 12:02:52,561 - INFO - tod_display - tod_display.py:384 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:02:52,586 - INFO - tod_display - tod_display.py:388 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:02:52,586 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:02:52,586 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 12:02:53,074 - INFO - tod_display - tod_display.py:439 - Successfully fetched 120 records for ToD consumption - Kids Clinic India Limited
2025-07-08 12:02:53,098 - INFO - tod_display - tod_display.py:449 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:02:53,128 - INFO - tod_display - tod_display.py:453 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:02:53,133 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 12:02:53,712 - INFO - tod_display - tod_display.py:500 - Successfully fetched 720 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 12:02:53,712 - INFO - tod_display - tod_display.py:509 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:02:53,750 - INFO - tod_display - tod_display.py:513 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:02:53,750 - INFO - tod_display - tod_display.py:565 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 12:28:14,657 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking area chart display for plant: Kids Clinic India Limited
2025-07-08 12:28:14,657 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 12:28:14,930 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 12:28:14,931 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking area chart for Kids Clinic India Limited
2025-07-08 12:28:15,024 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking area chart for Kids Clinic India Limited
2025-07-08 12:28:15,028 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 12:28:16,261 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:28:16,262 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 12:28:16,262 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:28:16,309 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:28:16,309 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:28:16,313 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 12:28:16,315 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 12:28:16,315 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 12:28:16,325 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 12:28:16,331 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:16,512 - INFO - tod_display - tod_display.py:319 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 12:28:16,512 - INFO - tod_display - tod_display.py:328 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:28:17,090 - INFO - tod_display - tod_display.py:332 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:28:17,107 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:17,107 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 12:28:17,310 - INFO - tod_display - tod_display.py:374 - Successfully fetched 120 records for ToD generation - Kids Clinic India Limited
2025-07-08 12:28:17,310 - INFO - tod_display - tod_display.py:384 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:28:17,328 - INFO - tod_display - tod_display.py:388 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:28:17,341 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:17,341 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 12:28:17,555 - INFO - tod_display - tod_display.py:439 - Successfully fetched 120 records for ToD consumption - Kids Clinic India Limited
2025-07-08 12:28:17,555 - INFO - tod_display - tod_display.py:449 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:28:17,595 - INFO - tod_display - tod_display.py:453 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:28:17,599 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 12:28:18,060 - INFO - tod_display - tod_display.py:500 - Successfully fetched 720 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 12:28:18,060 - INFO - tod_display - tod_display.py:509 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:28:18,108 - INFO - tod_display - tod_display.py:513 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:28:18,132 - INFO - tod_display - tod_display.py:565 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 12:28:27,395 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 12:28:27,396 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 12:28:27,709 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 12:28:27,710 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:28:27,757 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:28:27,761 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 12:28:28,774 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:28:28,774 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 12:28:28,775 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:28:28,795 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:28:28,797 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:28:28,799 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 12:28:28,800 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 12:28:28,803 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 12:28:28,806 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 12:28:28,809 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:29,002 - INFO - tod_display - tod_display.py:319 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 12:28:29,002 - INFO - tod_display - tod_display.py:328 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:28:29,301 - INFO - tod_display - tod_display.py:332 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:28:29,304 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:29,304 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 12:28:29,498 - INFO - tod_display - tod_display.py:374 - Successfully fetched 120 records for ToD generation - Kids Clinic India Limited
2025-07-08 12:28:29,500 - INFO - tod_display - tod_display.py:384 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:28:29,517 - INFO - tod_display - tod_display.py:388 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:28:29,520 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:29,520 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 12:28:29,697 - INFO - tod_display - tod_display.py:439 - Successfully fetched 120 records for ToD consumption - Kids Clinic India Limited
2025-07-08 12:28:29,698 - INFO - tod_display - tod_display.py:449 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:28:29,715 - INFO - tod_display - tod_display.py:453 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:28:29,717 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 12:28:30,081 - INFO - tod_display - tod_display.py:500 - Successfully fetched 720 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 12:28:30,082 - INFO - tod_display - tod_display.py:509 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:28:30,104 - INFO - tod_display - tod_display.py:513 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:28:30,112 - INFO - tod_display - tod_display.py:565 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 14:54:06,383 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 14:54:06,389 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 14:54:06,679 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 14:54:06,680 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 14:54:06,775 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 14:54:06,795 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 14:54:08,446 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 14:54:08,446 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 14:54:08,446 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 14:54:08,497 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 14:54:08,497 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 14:54:08,497 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 14:54:08,501 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 14:54:08,503 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 14:54:08,507 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 14:54:08,512 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,575 - WARNING - tod_display - tod_display.py:315 - No ToD generation vs consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,579 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,580 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 14:54:08,649 - WARNING - tod_display - tod_display.py:370 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,652 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,652 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 14:54:08,735 - WARNING - tod_display - tod_display.py:435 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,739 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 14:54:08,820 - WARNING - tod_display - tod_display.py:496 - No hourly generation data available for plant: Kids Clinic India Limited
2025-07-08 14:54:12,182 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 14:54:12,183 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 14:54:12,528 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 14:54:12,528 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 14:54:13,812 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 14:54:13,812 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 14:54:14,118 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 14:54:14,118 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 14:54:14,221 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 14:54:14,221 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 14:54:15,874 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 14:54:15,874 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 14:54:15,874 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 14:54:15,902 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 14:54:15,902 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 14:54:15,902 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 14:54:15,902 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 14:54:15,913 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 14:54:15,920 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 14:54:15,925 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 14:54:16,103 - INFO - tod_display - tod_display.py:319 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 14:54:16,103 - INFO - tod_display - tod_display.py:328 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 14:54:16,647 - INFO - tod_display - tod_display.py:332 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 14:54:16,647 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 14:54:16,647 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 14:54:16,828 - INFO - tod_display - tod_display.py:374 - Successfully fetched 120 records for ToD generation - Kids Clinic India Limited
2025-07-08 14:54:16,828 - INFO - tod_display - tod_display.py:384 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 14:54:16,879 - INFO - tod_display - tod_display.py:388 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 14:54:16,879 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 14:54:16,879 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 14:54:17,065 - INFO - tod_display - tod_display.py:439 - Successfully fetched 120 records for ToD consumption - Kids Clinic India Limited
2025-07-08 14:54:17,066 - INFO - tod_display - tod_display.py:449 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 14:54:17,095 - INFO - tod_display - tod_display.py:453 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 14:54:17,104 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 14:54:17,591 - INFO - tod_display - tod_display.py:500 - Successfully fetched 720 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 14:54:17,592 - INFO - tod_display - tod_display.py:509 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 14:54:17,630 - INFO - tod_display - tod_display.py:513 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 14:54:17,665 - INFO - tod_display - tod_display.py:565 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 15:48:10,771 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:48:12,371 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:48:12,371 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 15:48:12,371 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:48:12,620 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:48:12,620 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:48:12,637 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 15:48:12,637 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 15:48:12,637 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 15:48:12,655 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 15:48:12,659 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:12,659 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 15:48:12,728 - WARNING - tod_display - tod_display.py:372 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:12,728 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:12,728 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 15:48:12,810 - WARNING - tod_display - tod_display.py:437 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:12,924 - INFO - tod_display - tod_display.py:38 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 15:48:12,925 - INFO - tod_display - tod_display.py:48 - Using interactive plots: True
2025-07-08 15:48:13,260 - INFO - tod_display - tod_display.py:58 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 15:48:13,260 - INFO - tod_display - tod_display.py:68 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 15:48:13,454 - INFO - tod_display - tod_display.py:72 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 15:48:13,454 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:13,523 - WARNING - tod_display - tod_display.py:317 - No ToD generation vs consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:13,526 - INFO - tod_display - tod_display.py:481 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 15:48:13,585 - WARNING - tod_display - tod_display.py:498 - No hourly generation data available for plant: Kids Clinic India Limited
2025-07-08 15:48:13,585 - INFO - tod_display - tod_display.py:589 - Starting monthly settled heatmap display for plant: Kids Clinic India Limited
2025-07-08 15:48:13,585 - INFO - tod_display - tod_display.py:599 - Using interactive plots: True
2025-07-08 15:48:13,988 - WARNING - tod_display - tod_display.py:611 - 'settled' column not found in data for plant: Kids Clinic India Limited
2025-07-08 15:48:14,386 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:48:15,553 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:48:16,274 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:48:17,754 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:48:17,754 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 15:48:17,754 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:48:17,804 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:48:17,804 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:48:17,804 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 15:48:17,804 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 15:48:17,820 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 15:48:17,826 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 15:48:17,828 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:48:17,830 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 15:48:18,018 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 15:48:18,018 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:48:18,085 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:48:18,088 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:48:18,088 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 15:48:18,253 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 15:48:18,253 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:48:18,304 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:48:19,125 - INFO - tod_display - tod_display.py:38 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 15:48:19,127 - INFO - tod_display - tod_display.py:48 - Using interactive plots: True
2025-07-08 15:48:19,443 - INFO - tod_display - tod_display.py:58 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 15:48:19,444 - INFO - tod_display - tod_display.py:68 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 15:48:19,520 - INFO - tod_display - tod_display.py:72 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 15:48:19,537 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:48:19,703 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 15:48:19,703 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:48:20,207 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:48:20,211 - INFO - tod_display - tod_display.py:481 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 15:48:20,687 - INFO - tod_display - tod_display.py:502 - Successfully fetched 744 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 15:48:20,687 - INFO - tod_display - tod_display.py:511 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 15:48:20,739 - INFO - tod_display - tod_display.py:515 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 15:48:20,759 - INFO - tod_display - tod_display.py:567 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 15:48:20,759 - INFO - tod_display - tod_display.py:589 - Starting monthly settled heatmap display for plant: Kids Clinic India Limited
2025-07-08 15:48:20,759 - INFO - tod_display - tod_display.py:599 - Using interactive plots: True
2025-07-08 15:48:21,038 - WARNING - tod_display - tod_display.py:611 - 'settled' column not found in data for plant: Kids Clinic India Limited
2025-07-08 15:50:57,623 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:50:58,683 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:50:58,685 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 15:50:58,685 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:50:58,704 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:50:58,704 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:50:58,707 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 15:50:58,708 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 15:50:58,709 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 15:50:58,714 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 15:50:58,718 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:50:58,718 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 15:50:58,882 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 15:50:58,897 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:50:58,917 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:50:58,919 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:50:58,920 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 15:50:59,109 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 15:50:59,109 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:50:59,124 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:50:59,421 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:50:59,645 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 15:50:59,645 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:50:59,973 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:50:59,984 - INFO - tod_display - tod_display.py:481 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 15:51:00,382 - INFO - tod_display - tod_display.py:502 - Successfully fetched 744 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 15:51:00,382 - INFO - tod_display - tod_display.py:511 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 15:51:00,411 - INFO - tod_display - tod_display.py:515 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 15:51:00,424 - INFO - tod_display - tod_display.py:567 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 15:51:00,429 - INFO - tod_display - tod_display.py:589 - Starting monthly settled heatmap display for plant: Kids Clinic India Limited
2025-07-08 15:51:00,429 - INFO - tod_display - tod_display.py:599 - Using interactive plots: True
2025-07-08 15:51:00,803 - WARNING - tod_display - tod_display.py:611 - 'settled' column not found in data for plant: Kids Clinic India Limited
2025-07-08 15:51:16,000 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:51:17,067 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:51:17,067 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 15:51:17,067 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:51:17,093 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:51:17,093 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:51:17,096 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 15:51:17,096 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 15:51:17,098 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 15:51:17,104 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 15:51:17,107 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:51:17,108 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 15:51:17,284 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 15:51:17,284 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:51:17,306 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:51:17,315 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:51:17,315 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 15:51:17,485 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 15:51:17,485 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:51:17,504 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:51:17,788 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:51:17,956 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 15:51:17,956 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:51:18,223 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:53:06,910 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:53:07,876 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:53:07,876 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 15:53:07,876 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:53:07,896 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:53:07,896 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:53:07,902 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 15:53:07,902 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 15:53:07,904 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 15:53:07,908 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 15:53:07,912 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:53:07,913 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 15:53:08,112 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 15:53:08,112 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:53:08,131 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:53:08,134 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:53:08,135 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 15:53:08,345 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 15:53:08,345 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:53:08,379 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:53:08,697 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:53:08,906 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 15:53:08,907 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:53:09,195 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:03:09,559 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:03:10,520 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:03:10,520 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:03:10,520 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:03:10,545 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:03:10,545 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:03:10,547 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:03:10,547 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:03:10,551 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:03:10,557 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:03:10,561 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:03:10,561 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:03:10,824 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:03:10,824 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:03:10,849 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:03:10,877 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:03:10,877 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:03:11,108 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:03:11,108 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:03:11,129 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:03:11,426 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:03:11,667 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:03:11,668 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:03:11,941 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:04:51,790 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:04:52,847 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:04:52,848 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:04:52,848 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:04:52,867 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:04:52,868 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:04:52,869 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:04:52,870 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:04:52,870 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:04:52,876 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:04:52,879 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:04:52,879 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:04:53,175 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:04:53,176 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:04:53,198 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:04:53,202 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:04:53,202 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:04:53,503 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:04:53,503 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:04:53,516 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:04:53,813 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:04:53,994 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:04:53,994 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:04:54,286 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:05:24,604 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:05:24,859 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:05:24,859 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:05:25,194 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:05:48,715 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:05:49,674 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:05:49,675 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:05:49,675 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:05:49,695 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:05:49,696 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:05:49,698 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:05:49,698 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:05:49,701 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:05:49,704 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:05:49,707 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:05:49,708 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:05:49,938 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:05:49,938 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:05:49,957 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:05:49,960 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:05:49,960 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:05:50,215 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:05:50,216 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:05:50,235 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:05:50,554 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:05:50,817 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:05:50,817 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:05:51,132 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:17,188 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:07:18,223 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:18,223 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:07:18,223 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:18,247 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:18,247 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:18,251 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:07:18,252 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:07:18,254 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:07:18,260 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:07:18,263 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:18,263 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:07:18,492 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:07:18,493 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:18,511 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:18,516 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:18,516 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:07:18,742 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:07:18,743 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:18,765 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:19,095 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:19,363 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:07:19,364 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:19,639 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:48,942 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:07:49,957 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:49,957 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:07:49,957 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:49,975 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:49,975 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:49,991 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:07:49,993 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:07:49,996 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:07:49,999 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:07:50,002 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:50,002 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:07:50,262 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:07:50,262 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:50,278 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:50,288 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:50,288 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:07:50,654 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:07:50,654 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:50,685 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:51,130 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:51,372 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:07:51,372 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:51,653 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:53,031 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:07:54,031 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:54,032 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:07:54,032 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:54,048 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:54,048 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:54,048 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:07:54,048 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:07:54,048 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:07:54,061 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:07:54,064 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:54,064 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:07:54,291 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:07:54,292 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:54,308 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:54,313 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:54,314 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:07:54,530 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:07:54,530 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:54,546 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:54,831 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:55,071 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:07:55,072 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:55,373 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:08:29,333 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:08:30,281 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:08:30,281 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:08:30,281 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:08:30,307 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:08:30,310 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:08:30,312 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:08:30,313 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:08:30,316 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:08:30,321 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:08:30,324 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:08:30,326 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:08:30,526 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:08:30,527 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:08:30,544 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:08:30,547 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:08:30,548 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:08:30,791 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:08:30,791 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:08:30,812 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:08:31,067 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:08:31,257 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:08:31,257 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:08:31,532 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:09:42,545 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:09:43,566 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:09:43,582 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:09:43,582 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:09:43,599 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:09:43,600 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:09:43,601 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:09:43,602 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:09:43,605 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:09:43,610 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:09:43,611 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:09:43,611 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:09:43,867 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:09:43,867 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:09:43,886 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:09:43,891 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:09:43,891 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:09:44,119 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:09:44,119 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:09:44,158 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:09:44,494 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:09:44,699 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:09:44,700 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:09:44,976 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:10:37,156 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:10:38,159 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:10:38,160 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:10:38,160 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:10:38,178 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:10:38,180 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:10:38,182 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:10:38,183 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:10:38,187 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:10:38,191 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:10:38,196 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:10:38,196 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:10:38,436 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:10:38,436 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:10:38,468 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:10:38,471 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:10:38,471 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:10:38,721 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:10:38,722 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:10:38,739 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:10:39,106 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:10:39,370 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:10:39,370 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:10:39,622 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited

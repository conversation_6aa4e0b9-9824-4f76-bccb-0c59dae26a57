import pandas as pd
from db.safe_db_utils import safe_read_sql
from helper.setup_logger import setup_logger

logging = setup_logger("unit_wise_power_cost_calculations", "unit_wise_power_cost_calculations.log")


def fetch_unitwise_monthly_data(
    conn,
    client_name: str = None
) -> pd.DataFrame:
    """
    Fetch monthly aggregated data for consumption and settled amounts grouped by cons_unit.

    Args:
        conn: MySQL connection object
        client_name (str, optional): Filter by client name

    Returns:
        pd.DataFrame: DataFrame with unit-wise month-wise consumption and settlement data
    """

    # --- Fetch unit-wise consumption and settlement data ---
    query = """
        SELECT
            cons_unit,
            date,
            consumption,
            allocated_generation,
            settled
        FROM
            settlement_data
        WHERE
            date IS NOT NULL
            {client_filter}
        ORDER BY
            cons_unit, date;
    """

    # Prepare query and params
    params = ()
    if client_name:
        query = query.format(client_filter="AND client_name = %s")
        params = (client_name,)
    else:
        query = query.format(client_filter="")

    # Read data
    df = safe_read_sql(query, conn, params)
    if df.empty:
        logging.warning("No data found")
        return pd.DataFrame()

    # Convert date and create month column
    df['date'] = pd.to_datetime(df['date'])
    df['month'] = df['date'].dt.to_period('M').astype(str)

    # Group by cons_unit and month
    df_monthly = df.groupby(['cons_unit', 'month'], as_index=False).agg({
        'consumption': 'sum',
        'allocated_generation': 'sum',
        'settled': 'sum'
    }).rename(columns={
        'consumption': 'total_consumption_sum',
        'allocated_generation': 'total_allocated_generation',
        'settled': 'total_settled'
    })

    return df_monthly


def calculate_unitwise_monthly_power_costs(df: pd.DataFrame, grid_rate_per_kwh: float = 4.0) -> pd.DataFrame:
    """
    Calculate unit-wise monthly cost metrics and return in clean format with renamed columns.

    Args:
        df (pd.DataFrame): DataFrame with columns:
            ['cons_unit', 'month', 'total_consumption_sum', 'total_allocated_generation', 'total_settled']
        grid_rate_per_kwh (float): Grid rate in ₹ per kWh

    Returns:
        pd.DataFrame: Formatted with required columns
    """
    if df.empty:
        return pd.DataFrame()

    df = df.copy()

    # Fill NaNs to ensure safe arithmetic
    for col in ['total_consumption_sum', 'total_allocated_generation', 'total_settled']:
        df[col] = df[col].fillna(0)

    # Net consumption = what still had to be bought from the grid after settlement
    df['grid_consumption'] = (df['total_consumption_sum'] - df['total_settled']).clip(lower=0)

    # Costs
    df['grid_cost'] = df['total_consumption_sum'] * grid_rate_per_kwh
    df['actual_cost'] = df['grid_consumption'] * grid_rate_per_kwh
    df['savings'] = df['grid_cost'] - df['actual_cost']
    df['savings_percentage'] = df.apply(
        lambda row: round(row['savings'] / row['grid_cost'] * 100, 2) if row['grid_cost'] > 0 else 0,
        axis=1
    )

    # Final formatting
    df_final = df[[
        'cons_unit',
        'month',
        'grid_cost',
        'actual_cost',
        'savings',
        'total_settled',
        'savings_percentage'
    ]].rename(columns={
        'cons_unit': 'Unit',
        'month': 'Month',
        'grid_cost': 'Grid Cost (₹)',
        'actual_cost': 'Actual Cost (₹)',
        'savings': 'Savings (₹)',
        'total_settled': 'Energy Offset (kWh)',
        'savings_percentage': 'Savings (%)'
    })

    return df_final


def create_unitwise_monthly_bill_table(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create a pivot table showing unit-wise monthly bills with grid power cost and after adjustment.
    
    Args:
        df (pd.DataFrame): Unit-wise monthly cost data
    
    Returns:
        pd.DataFrame: Pivot table with units as rows and months as columns
    """
    if df.empty:
        return pd.DataFrame()
    
    # Create pivot table for Grid Cost
    grid_cost_pivot = df.pivot_table(
        index='Unit',
        columns='Month',
        values='Grid Cost (₹)',
        fill_value=0,
        aggfunc='sum'
    )
    
    # Create pivot table for Actual Cost (after adjustment)
    actual_cost_pivot = df.pivot_table(
        index='Unit',
        columns='Month',
        values='Actual Cost (₹)',
        fill_value=0,
        aggfunc='sum'
    )
    
    # Combine both tables with multi-level columns
    combined_table = pd.concat([
        grid_cost_pivot.add_suffix(' (Grid)'),
        actual_cost_pivot.add_suffix(' (Adjusted)')
    ], axis=1)
    
    # Sort columns chronologically
    columns = sorted(combined_table.columns, key=lambda x: (x.split(' ')[0], x.split(' ')[1]))
    combined_table = combined_table[columns]
    
    # Add totals row
    totals = combined_table.sum()
    combined_table.loc['TOTAL'] = totals
    
    return combined_table


def summarize_unitwise_costs_table(df: pd.DataFrame) -> pd.DataFrame:
    """
    Summarize unit-wise cost metrics from monthly-level DataFrame.
    
    Args:
        df (pd.DataFrame): Unit-wise monthly cost data
    
    Returns:
        pd.DataFrame: Summary table with unit-wise totals
    """
    if df.empty:
        return pd.DataFrame()
    
    summary = df.groupby('Unit').agg({
        'Grid Cost (₹)': 'sum',
        'Actual Cost (₹)': 'sum',
        'Savings (₹)': 'sum',
        'Energy Offset (kWh)': 'sum'
    }).reset_index()
    
    # Calculate overall savings percentage
    summary['Savings (%)'] = summary.apply(
        lambda row: round(row['Savings (₹)'] / row['Grid Cost (₹)'] * 100, 2) 
        if row['Grid Cost (₹)'] > 0 else 0, 
        axis=1
    )
    
    # Add total row
    total_row = {
        'Unit': 'TOTAL',
        'Grid Cost (₹)': summary['Grid Cost (₹)'].sum(),
        'Actual Cost (₹)': summary['Actual Cost (₹)'].sum(),
        'Savings (₹)': summary['Savings (₹)'].sum(),
        'Energy Offset (kWh)': summary['Energy Offset (kWh)'].sum(),
        'Savings (%)': round(summary['Savings (₹)'].sum() / summary['Grid Cost (₹)'].sum() * 100, 2) 
        if summary['Grid Cost (₹)'].sum() > 0 else 0
    }
    
    summary = pd.concat([summary, pd.DataFrame([total_row])], ignore_index=True)
    
    return summary
